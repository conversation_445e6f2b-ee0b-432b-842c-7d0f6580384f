"use client";

import { useRef, useState, useCallback } from 'react';
import QrScanner from 'qr-scanner';

interface UseQRScannerOptions {
  onScanSuccess?: (result: string) => void;
  onScanError?: (error: string) => void;
}

interface UseQRScannerReturn {
  isScanning: boolean;
  error: string | null;
  videoRef: React.RefObject<HTMLVideoElement>;
  startScanning: () => Promise<void>;
  stopScanning: () => void;
  hasCamera: () => Promise<boolean>;
}

/**
 * Custom hook for QR code scanning functionality
 * 
 * Provides camera access, QR code scanning, and error handling
 * for web-based QR code reading using the qr-scanner library.
 */
export const useQRScanner = (options: UseQRScannerOptions = {}): UseQRScannerReturn => {
  const { onScanSuccess, onScanError } = options;
  
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const qrScannerRef = useRef<QrScanner | null>(null);

  /**
   * Check if camera is available
   */
  const hasCamera = useCallback(async (): Promise<boolean> => {
    try {
      return await QrScanner.hasCamera();
    } catch {
      return false;
    }
  }, []);

  /**
   * Start QR code scanning
   */
  const startScanning = useCallback(async (): Promise<void> => {
    if (!videoRef.current || isScanning) return;

    try {
      setError(null);
      setIsScanning(true);

      // Check camera availability
      const cameraAvailable = await hasCamera();
      if (!cameraAvailable) {
        throw new Error('No camera found. Please ensure camera permissions are granted.');
      }

      // Create QR Scanner instance
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => {
          if (onScanSuccess) {
            onScanSuccess(result.data);
          }
        },
        {
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment', // Use back camera if available
          maxScansPerSecond: 5,
        }
      );

      // Start scanning
      await qrScannerRef.current.start();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start camera';
      setError(errorMessage);
      setIsScanning(false);
      
      if (onScanError) {
        onScanError(errorMessage);
      }
    }
  }, [isScanning, hasCamera, onScanSuccess, onScanError]);

  /**
   * Stop QR code scanning
   */
  const stopScanning = useCallback((): void => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop();
      qrScannerRef.current.destroy();
      qrScannerRef.current = null;
    }
    setIsScanning(false);
    setError(null);
  }, []);

  return {
    isScanning,
    error,
    videoRef,
    startScanning,
    stopScanning,
    hasCamera,
  };
};

/**
 * Utility function to extract bet ID from QR code data
 * 
 * Supports various formats:
 * - Full betslip URLs: https://domain.com/betslip/bet-id
 * - Direct bet IDs: UUID format
 * - Custom bet ID formats
 */
export const extractBetIdFromQR = (qrData: string): string | null => {
  try {
    // Remove whitespace
    const cleanData = qrData.trim();
    
    // Check for betslip URL pattern
    const urlPatterns = [
      /\/betslip\/([a-zA-Z0-9-]+)/i,
      /\/bet\/([a-zA-Z0-9-]+)/i,
      /betId=([a-zA-Z0-9-]+)/i,
      /bet_id=([a-zA-Z0-9-]+)/i,
    ];
    
    for (const pattern of urlPatterns) {
      const match = cleanData.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    // Check if it's a direct UUID format
    const uuidPattern = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
    if (uuidPattern.test(cleanData)) {
      return cleanData;
    }
    
    // Check for other common bet ID formats
    const betIdPatterns = [
      /^[A-Z0-9]{6,20}$/i, // Alphanumeric bet IDs
      /^BET[0-9]{6,15}$/i, // BET prefix format
      /^[0-9]{8,20}$/,     // Numeric bet IDs
    ];
    
    for (const pattern of betIdPatterns) {
      if (pattern.test(cleanData)) {
        return cleanData;
      }
    }
    
    return null;
  } catch {
    return null;
  }
};

/**
 * Utility function to validate bet ID format
 */
export const isValidBetId = (betId: string): boolean => {
  if (!betId || typeof betId !== 'string') return false;
  
  const cleanBetId = betId.trim();
  if (cleanBetId.length < 6) return false;
  
  // Check common bet ID patterns
  const validPatterns = [
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i, // UUID
    /^[A-Z0-9]{6,20}$/i, // Alphanumeric
    /^BET[0-9]{6,15}$/i, // BET prefix
    /^[0-9]{8,20}$/,     // Numeric
  ];
  
  return validPatterns.some(pattern => pattern.test(cleanBetId));
};
