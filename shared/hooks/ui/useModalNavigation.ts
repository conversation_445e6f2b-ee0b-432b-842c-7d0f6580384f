import { useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';

/**
 * Modal navigation parameters interface
 */
interface ModalParams {
  modal: string;
  mode?: string;
  entityId?: string | number;
  [key: string]: string | number | undefined;
}

/**
 * Modal navigation hook return interface
 */
interface UseModalNavigationReturn {
  // Generic modal opening function
  handleModalClick: (params: ModalParams) => void;

  // User management specific functions
  openCreateUserModal: () => void;
  openEditUserModal: (userId: string | number) => void;
  openDeactivateUserModal: (userId: string | number) => void;
  openActivateUserModal: (userId: string | number) => void;

  // Wallet transaction specific functions
  openWalletTransactionModal: (userId?: string | number) => void;

  // Check result specific function
  openCheckResultModal: () => void;

  // Generic utility for any modal type
  openModal: (modalType: string, mode?: string, entityId?: string | number, additionalParams?: Record<string, string>) => void;

  // Close modal function
  closeModal: () => void;
}

/**
 * Custom hook for centralized modal navigation
 * 
 * This hook provides a unified API for opening modals throughout the application.
 * It handles URL parameter construction and navigation using Next.js router.
 * 
 * Features:
 * - Centralized modal opening logic
 * - URL parameter-based modal system
 * - Support for different modal types and modes
 * - Convenience methods for common operations
 * - Maintains current page context
 * 
 * Usage:
 * ```tsx
 * const { openCreateUserModal, openEditUserModal, handleModalClick } = useModalNavigation();
 * 
 * // Using convenience methods
 * openCreateUserModal();
 * openEditUserModal(123);
 * 
 * // Using generic method
 * handleModalClick({ modal: 'user-management', mode: 'create' });
 * ```
 */
export const useModalNavigation = (): UseModalNavigationReturn => {
  const router = useRouter();
  const pathname = usePathname();

  /**
   * Generic modal opening function that constructs URL with parameters
   */
  const handleModalClick = useCallback((params: ModalParams) => {
    const currentUrl = new URL(window.location.href);

    // Clear existing modal parameters
    currentUrl.searchParams.delete('modal');
    currentUrl.searchParams.delete('mode');
    currentUrl.searchParams.delete('userId');
    currentUrl.searchParams.delete('entityId');

    // Set new modal parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        // Handle entityId mapping to specific parameter names
        if (key === 'entityId' && params.modal === 'user-management') {
          currentUrl.searchParams.set('userId', value.toString());
        } else if (key === 'entityId') {
          currentUrl.searchParams.set('entityId', value.toString());
        } else {
          currentUrl.searchParams.set(key, value.toString());
        }
      }
    });

    // Navigate to the new URL
    const newUrl = `${pathname}${currentUrl.search}`;
    router.push(newUrl, { scroll: false });
  }, [router, pathname]);

  /**
   * Generic modal opening utility
   */
  const openModal = useCallback((
    modalType: string,
    mode?: string,
    entityId?: string | number,
    additionalParams?: Record<string, string>
  ) => {
    const params: ModalParams = {
      modal: modalType,
      ...(mode && { mode }),
      ...(entityId && { entityId }),
      ...additionalParams
    };

    handleModalClick(params);
  }, [handleModalClick]);

  /**
   * User Management Modal Functions
   */
  const openCreateUserModal = useCallback(() => {
    handleModalClick({
      modal: 'user-management',
      mode: 'create'
    });
  }, [handleModalClick]);

  const openEditUserModal = useCallback((userId: string | number) => {
    handleModalClick({
      modal: 'user-management',
      mode: 'edit',
      entityId: userId
    });
  }, [handleModalClick]);

  const openDeactivateUserModal = useCallback((userId: string | number) => {
    handleModalClick({
      modal: 'user-management',
      mode: 'deactivate',
      entityId: userId
    });
  }, [handleModalClick]);

  const openActivateUserModal = useCallback((userId: string | number) => {
    handleModalClick({
      modal: 'user-management',
      mode: 'activate',
      entityId: userId
    });
  }, [handleModalClick]);

  /**
   * Wallet Transaction Modal Functions
   */
  const openWalletTransactionModal = useCallback((userId?: string | number) => {
    const params: ModalParams = {
      modal: 'wallet-transaction'
    };

    if (userId) {
      params.entityId = userId;
    }

    handleModalClick(params);
  }, [handleModalClick]);

  /**
   * Close modal by removing all modal parameters
   */
  const closeModal = useCallback(() => {
    const currentUrl = new URL(window.location.href);

    // Remove all modal-related parameters
    currentUrl.searchParams.delete('modal');
    currentUrl.searchParams.delete('mode');
    currentUrl.searchParams.delete('userId');
    currentUrl.searchParams.delete('entityId');

    // Navigate to clean URL
    const newUrl = `${pathname}${currentUrl.search}`;
    router.push(newUrl, { scroll: false });
  }, [router, pathname]);

  return {
    handleModalClick,
    openCreateUserModal,
    openEditUserModal,
    openDeactivateUserModal,
    openActivateUserModal,
    openWalletTransactionModal,
    openModal,
    closeModal,
  };
};
