import { BetDetailsData } from "@/shared/types";

interface Props extends BetDetailsData {
    qrCodeDataUrl: string;
}
export const PrintBetSlipUI = ({ qrCodeDataUrl, ...betDetailsData }: Props) => {

    return `
      <!DOCTYPE html>
      <html>
        <head>
        <title>BET SLIP</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 10px;
              background-color: white;
              color: black;
              line-height: 1.6;
              font-size: 10px;
            }
            .header {
              text-align: center;
              margin-bottom: 5px;
              position: relative;
            }
            .title {
              color: black;
              font-size: 14px;
              font-weight: bold;
              margin: 0;
            }
            .bet-slip-id {
              position: absolute;
              top: 0;
              right: 0;
              font-size: 12px;
              font-weight: bold;
              color: black;
            }
            .subtitle {
              color: black;
              font-size: 12px;
              margin: 10px 0 0 0;
              font-weight: normal;
            }
            .section {
              margin: 10px 0;
              background-color: white;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              color: black;
              margin-bottom: 5px;
              border-bottom: 2px solid #000;
              text-transform: uppercase;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #ccc;
            }
            .detail-row:last-child {
              border-bottom: none;
            }
            .label {
              font-weight: bold;
              color: black;
              flex: 1;
              font-size: 10px;
            }
            .value {
              color: black;
              flex: 2;
              text-align: right;
              font-size: 10px;
            }
            .bet-list {
              margin-top: 5px;
            }
            .bet-item {
              background: white;
              padding: 5px;
              margin: 5px 0;
            }
            .qr-section {
              margin-top: 10px;
              text-align: center;
              padding:5px;
              border-top: 2px solid #000;
            }
            .qr-code {
              width: 100px;
              height: 100px;
              margin: 0 auto 10px;
              border: 2px solid #000;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: white;
              font-size: 14px;
              color: black;
            }
            .qr-label {
              font-size: 12px;
              color: black;
              font-weight: bold;
              margin-top: 5px;
            }
            .footer {
              margin-top: 5px;
              font-size: 10px;
              color: black;
              border-top: 2px solid #000;
              padding-top: 5px;
            }
            .footer-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin: 5px 0;
            }
            .footer-item {
              flex: 1;
              text-align: center;
            }
            @media print {
              body {
                margin: 5px;
                background-color: white !important;
                color: black !important;
              }
              .qr-section {
                break-inside: avoid;
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="bet-slip-id">Bet Slip - ${betDetailsData.betDetails?.betId || 'N/A'}</div>
            <p class="subtitle">Transaction Confirmation</p>
          </div>

          <!-- Market Details Section -->
          <div class="section">
            <div class="section-title">Market Information</div>
            <div class="detail-row">
              <span class="label">Market ID:</span>
              <span class="value">${betDetailsData.marketDetail?.marketId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Name:</span>
              <span class="value">${betDetailsData.marketDetail?.marketName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Status:</span>
              <span class="value">${betDetailsData.marketDetail?.marketStatus || 'N/A'}</span>
            </div>
          </div>

          <!-- Bet Details Section -->
          <div class="section">
            <div class="section-title">Bet Details</div>
            <div class="detail-row">
              <span class="label">Bet ID:</span>
              <span class="value">${betDetailsData.betDetails?.betId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Type:</span>
              <span class="value">${betDetailsData.betDetails?.betType || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Status:</span>
              <span class="value">${betDetailsData.betDetails?.settlementStatus || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.betAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.settlementAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Created Date:</span>
              <span class="value">${betDetailsData.betDetails?.createdDate ? new Date(betDetailsData.betDetails.createdDate).toLocaleString() : 'N/A'}</span>
            </div>
          </div>

          <!-- Individual Bets Section -->
          ${betDetailsData.betList && betDetailsData.betList.length > 0 ? `
          <div class="section">
            <div class="section-title">Individual Bets</div>
            <div class="bet-list">
              ${betDetailsData.betList.map((bet, index) => `
                <div class="bet-item">
                  <div class="detail-row">
                    <span class="label">Bet ${index + 1} ID:</span>
                    <span class="value">${bet.betId || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Market Name:</span>
                    <span class="value">${bet.marketName || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Bet Type:</span>
                    <span class="value">${bet.betType || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Rate:</span>
                    <span class="value">${bet.rate || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Stake:</span>
                    <span class="value">$${(bet.stake || 0).toFixed(2)}</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          ` : ''}

          <!-- QR Code Section -->
          ${qrCodeDataUrl ? `
          <div class="qr-section">
            <img src="${qrCodeDataUrl}" alt="Bet Verification QR Code" class="qr-code" style="width: 100px; height: 100px; margin: 0 auto; display: block;" />
            <div class="qr-label">Scan for bet verification</div>
          </div>
          ` : `
          <div class="qr-section">
            <div class="qr-code">QR CODE</div>
            <div class="qr-label">Scan for bet verification</div>
          </div>
          `}

          <div class="footer">
            <p style="text-align: center; margin-bottom: 10px;"><strong>Thank you for your bet. Good luck!</strong></p>
            <div class="footer-row">
              <div class="footer-item">This is an automatically generated bet slip.</div>
              <div class="footer-item">Printed on: 22/07/2025, 21:51:37</div>
              <div class="footer-item">Printer: Default System Printer</div>
            </div>
          </div>
        </body>
      </html>
    `;
};
