"use client";

import React, { useState, useEffect } from 'react';
import { BaseModal } from '@/shared/UI/modals';
import { PrimaryButton } from '@/shared/UI/buttons';
import { SpkFormInput } from '@/shared/UI/forms';
import { SVGLoader } from '@/shared/UI/components/icons';
import { useQRScanner, extractBetIdFromQR, isValidBetId } from '@/shared/hooks/business/useQRScanner';
import { useBetResultQuery, useMarkBetAsSettledMutation, formatBetResultForDisplay } from '@/shared/query/useBetResultQuery';

interface CheckResultModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Remove the local BetResult interface as we're using the one from the API

/**
 * CheckResultModal Component
 * 
 * A modal for checking bet results via QR code scanning or manual bet ID input.
 * Features:
 * - 400px height modal
 * - QR scanner functionality with camera access
 * - Manual bet ID input
 * - Results display with win/loss status
 * - Scrollable bet list
 * - Mark as settled functionality
 */
const CheckResultModal: React.FC<CheckResultModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [currentState, setCurrentState] = useState<'input' | 'results'>('input');
  const [betId, setBetId] = useState('');
  const [searchBetId, setSearchBetId] = useState('');
  const [error, setError] = useState<string | null>(null);

  // QR Scanner hook
  const { isScanning, error: scanError, videoRef, startScanning, stopScanning } = useQRScanner({
    onScanSuccess: (qrData) => {
      const extractedBetId = extractBetIdFromQR(qrData);
      if (extractedBetId && isValidBetId(extractedBetId)) {
        setBetId(extractedBetId);
        setSearchBetId(extractedBetId);
        stopScanning();
      } else {
        setError('Invalid QR code format. Please scan a valid bet QR code.');
      }
    },
    onScanError: (scanErrorMsg) => {
      setError(scanErrorMsg);
    },
  });

  // Bet result query
  const { data: betResultResponse, isLoading, error: queryError } = useBetResultQuery(
    searchBetId,
    !!searchBetId && currentState === 'results'
  );

  // Mark as settled mutation
  const markAsSettledMutation = useMarkBetAsSettledMutation();

  // Handle QR scanner initialization
  const handleQRScannerClick = async () => {
    setError(null);
    await startScanning();
  };

  // Handle search for bet details
  const handleSearch = async () => {
    if (!betId.trim()) {
      setError('Please enter a bet ID');
      return;
    }

    if (!isValidBetId(betId)) {
      setError('Invalid bet ID format');
      return;
    }

    setError(null);
    setSearchBetId(betId);
    setCurrentState('results');
  };

  // Handle mark as settled
  const handleMarkAsSettled = async () => {
    if (!searchBetId) return;

    try {
      await markAsSettledMutation.mutateAsync(searchBetId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark as settled');
    }
  };

  // Handle share/copy
  const handleShare = () => {
    if (betResultResponse?.data) {
      navigator.clipboard.writeText(`Bet ID: ${betResultResponse.data.betId}`);
    }
  };

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      setCurrentState('input');
      setBetId('');
      setSearchBetId('');
      setError(null);
      stopScanning();
    }
  }, [isOpen, stopScanning]);

  // Handle errors from API or scanning
  useEffect(() => {
    if (queryError) {
      setError(queryError instanceof Error ? queryError.message : 'Failed to fetch bet details');
    } else if (scanError) {
      setError(scanError);
    }
  }, [queryError, scanError]);

  // Get formatted bet result data
  const betResult = betResultResponse?.data ? formatBetResultForDisplay(betResultResponse.data) : null;

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Check Result"
      size="md"
      width="462px"
      maxHeight="400px"
      className="check-result-modal"
    >
      <div className="flex flex-col h-full">
        {currentState === 'input' ? (
          // Initial State - Input Section
          <div className="flex-1 flex flex-col justify-center items-center gap-6 p-6">
            {/* QR Scanner Section */}
            <div className="flex items-center gap-6 w-full">
              {/* QR Scanner */}
              <div className="flex flex-col items-center gap-2">
                <div
                  className="w-[70px] h-[70px] bg-[#B0822A] rounded-lg flex items-center justify-center cursor-pointer relative overflow-hidden"
                  onClick={handleQRScannerClick}
                >
                  {isScanning ? (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-cover"
                      autoPlay
                      muted
                      playsInline
                    />
                  ) : (
                    <SVGLoader
                      name="scanner"
                      size="42px"
                      className="text-white"
                    />
                  )}
                </div>
                <span className="text-sm font-rubik text-white">Scan QR</span>
              </div>

              {/* OR Divider */}
              <div className="flex items-center gap-4 flex-1">
                <div className="h-px bg-gray-600 flex-1"></div>
                <span className="text-gray-400 font-rubik text-sm">OR</span>
                <div className="h-px bg-gray-600 flex-1"></div>
              </div>

              {/* Input Section */}
              <div className="flex flex-col gap-3 flex-1">
                <SpkFormInput
                  label="Bet ID"
                  value={betId}
                  onChange={(e) => setBetId(e.target.value)}
                  placeholder="Enter bet ID"
                  className="w-full"
                />
                <PrimaryButton
                  onClick={handleSearch}
                  loading={isLoading}
                  size="lg"
                  fullWidth
                >
                  Search
                </PrimaryButton>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="text-red-500 text-sm font-rubik text-center">
                {error}
              </div>
            )}
          </div>
        ) : (
          // Results State
          <div className="flex flex-col h-full">
            {/* Section 1 - Header */}
            <div className="p-4 border-b border-[#AF9660]">
              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <span className="text-white font-rubik text-sm">
                    {betResult?.formattedDate} . {betResult?.formattedTime}
                  </span>
                  <span className="text-gray-400 font-rubik text-sm">
                    Bet ID: {betResult?.betId}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {/* Share/Copy Buttons */}
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] bg-[#333335] rounded border border-gray-600 text-white font-rubik text-sm"
                  >
                    Copy
                  </button>
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] bg-[#333335] rounded border border-gray-600 text-white font-rubik text-sm"
                  >
                    Share
                  </button>
                  {/* Win/Loss Badge */}
                  <div className={`w-[78px] h-[46px] rounded flex items-center justify-center font-rubik font-semibold text-sm ${betResult?.status === 'win'
                    ? 'bg-[#00B800] text-white'
                    : betResult?.status === 'loss'
                      ? 'bg-red-600 text-white'
                      : 'bg-yellow-600 text-white'
                    }`}>
                    {betResult?.status === 'win' ? 'WIN' : betResult?.status === 'loss' ? 'LOSS' : betResult?.status?.toUpperCase()}
                  </div>
                </div>
              </div>
            </div>

            {/* Section 2 - Scrollable Bet List */}
            <div className="flex-1 overflow-y-auto p-4">
              {betResult?.betList.map((bet, index) => {
                // Parse marketName format
                const parts = bet.marketName.split(' -> ');
                const sport = parts[0] || '';
                const league = parts[1] || '';
                const match = parts[2] || '';
                const market = parts[3] || '';

                return (
                  <div key={index} className="flex justify-between items-start mb-4">
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="text-[#7B7B7B] font-rubik font-normal text-sm leading-none">
                        ⚽ {match}
                      </div>
                      <div className="text-golden font-rubik font-medium text-lg leading-none capitalize">
                        {sport} {league}
                      </div>
                      <div className="text-white font-normal text-lg leading-none text-center">
                        {market}
                      </div>
                    </div>
                    <div className="text-golden font-rubik font-semibold text-xl leading-none text-center uppercase">
                      {bet.rate}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Section 3 - Fixed Bottom */}
            <div className="bg-[#FFFFFF1A] rounded-lg p-4 m-4 flex flex-col gap-3">
              <div className="flex justify-between items-center">
                <div className="flex flex-col">
                  <span className="text-gray-400 font-rubik text-sm">Bet Amount</span>
                  <span className="text-white font-rubik font-semibold text-lg">
                    LKR {betResult?.betAmount?.toLocaleString()}
                  </span>
                </div>
                <div className="flex flex-col text-right">
                  <span className="text-gray-400 font-rubik text-sm">
                    {betResult?.status === 'win' ? 'Winning Amount' : 'Losing Amount'}
                  </span>
                  <span className={`font-rubik font-semibold text-lg ${betResult?.status === 'win' ? 'text-green-500' : 'text-red-500'
                    }`}>
                    LKR {(betResult?.status === 'win' ? betResult?.winningAmount : betResult?.losingAmount)?.toLocaleString()}
                  </span>
                </div>
              </div>
              <PrimaryButton
                onClick={handleMarkAsSettled}
                loading={markAsSettledMutation.isPending}
                fullWidth
                className={betResult?.isSettled ? 'bg-green-600 hover:bg-green-700' : ''}
              >
                {betResult?.isSettled ? 'Settled' : 'Mark as Settled'}
              </PrimaryButton>
            </div>
          </div>
        )}
      </div>
    </BaseModal>
  );
};

export default CheckResultModal;
