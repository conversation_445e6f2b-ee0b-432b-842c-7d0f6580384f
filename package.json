{"name": "starterkit", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "NODE_ENV=development next dev", "dev:fast": "NODE_ENV=development next dev --turbo", "build": "npm run type-check && npm run lint && next build", "build:fast": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "organize-imports": "eslint . --ext .ts,.tsx --fix --rule '@typescript-eslint/no-unused-vars: error' --quiet", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "quality-check": "npm run type-check && npm run organize-imports && npm run lint", "postcss": "sass ./public/assets/scss/styles.scss ./public/assets/css/styles.css && postcss ./public/assets/css/styles.css -o ./public/assets/css/styles.css", "sass": "sass ./public/assets/scss/:./public/assets/css/", "sass-min": "sass ./public/assets/scss/:./public/assets/css/ --style compressed", "prepare": "husky"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^7.1.2", "@react-input/mask": "^2.0.4", "@reduxjs/toolkit": "^2.5.0", "@simonwep/pickr": "^1.9.1", "@tailwindcss/forms": "^0.5.9", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.21.3", "@types/qrcode": "^1.5.5", "@yaireo/tagify": "^4.35.1", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "chart.js": "^4.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "filepond": "^4.32.8", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "leaflet": "^1.9.4", "next": "15.2.0", "preline": "^2.6.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-country-state-city": "^1.1.12", "react-datepicker": "^8.1.0", "react-dom": "^19.0.0", "react-dropdown-select": "^4.12.2", "react-filepond": "^7.1.3", "react-leaflet": "^5.0.0", "react-listbox": "^1.2.13", "react-number-format": "^5.4.4", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-uploader": "^3.43.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "sass": "^1.83.0", "shepherd.js": "^14.3.0", "simplebar-react": "^3.3.0", "suneditor": "^2.47.5", "suneditor-react": "^3.6.1", "swiper": "^11.2.8", "uploader": "^3.48.3", "web-vitals": "^5.0.3", "yet-another-react-lightbox": "^3.23.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/leaflet": "^1.9.19", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.2.0", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.3", "sharp": "^0.34.3", "tailwindcss": "^3.4.17", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix --max-warnings 0 --cache --cache-location node_modules/.cache/eslint", "*.{ts,tsx}": "bash -c 'echo \"Skipping TypeScript check for individual files - will run full project check\"'"}}